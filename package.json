{"name": "todo.project", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "nodemon backend/server.js ", "build": "npm install && npm install --prefix frontend && npm run build --prefix frontend", "start": "node backend/server.js"}, "type": "module", "repository": {"type": "git", "url": "git+https://github.com/chethan-gen/todo.project.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/chethan-gen/todo.project/issues"}, "homepage": "https://github.com/chethan-gen/todo.project#readme", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "mongoose": "^8.17.0"}, "devDependencies": {"nodemon": "^3.1.10"}}